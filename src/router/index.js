import Vue from 'vue'
import VueRouter from 'vue-router'
/* Layout */
import Layout from '@/layout/index'
import otherRouter from '@/router/asyncRoutes.js'

Vue.use(VueRouter)

/**
 * 注意
 * hidden 设置当前路由是否再侧栏上显示
 * alwaysShow 设置是否一直显示子栏，否则的话当侧栏只有一个的时候不使用dropDown的形式显示
 * redirect: '/index' 通过主动redirect的方式进入子栏目，实际路由为父 path + children.path + ... + children.path
 * name 是用于设置缓存的组件的 如果组件需要缓存必须设置路由的name与组件的name值一致 https://cn.vuejs.org/v2/api/#keep-alive
 * meta 信息
 *  title: 路由的名称，用于国际化显示
 *  icon 设置当前导航栏是否显示icon
 *  no_permission // 不走权限控制的路由true/false，当前设置优先级比permission高
 *  permission 当前路由的权限
 *  noCache 设置是否不缓存当前路由
 *  activeMenu 手動設置側欄的高亮模塊，用於不在側欄顯示的子欄目反顯父級菜單
 *默认侧栏是不区分多少个层级的，不过请不要超过3个层级，不然有显示问题
 *当前路由注册是会注册所有后台返回有权限的路由表的，只不过部分路由是通过navMenu栏进行切换展示的
 *注意： path的值不要用''这种tab切换会有问题的，地址栏会默认多个/字符结尾
 */

// 默认路由，
export const defaultRoutes = [
  // {
  //   path: '/',
  //   // redirect: '/home',
  //   name: 'Home',
  //   component: () => import(/* webpackChunkName: "home" */ '@/views/Home.vue')
  // }
  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/index',
  //   meta: { title: 'index', icon: 'el-icon-edit' },
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import(/* webpackChunkName: "index" */ '@/views/About'),
  //       name: 'index',
  //       meta: { title: 'index', icon: 'el-icon-edit' }
  //     }
  //   ]
  // },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import(/* webpackChunkName: "login" */ '@/views/login'),
    hidden: true,
    name: 'Login',
    meta: {
      title: 'Login',
      noCache: true
    }
  },
  {
    path: '/excel', // '/excel/:type' 不能直接用这种放顶级的地址，不然点击tab栏切换会新打开一个页面的
    component: Layout,
    // redirect: '/excel',
    name: 'ExcelBox',
    hidden: true,
    meta: { title: 'excel', icon: 'excel' },
    children: [
      {
        path: '/excel/:random?',
        component: () => import(/* webpackChunkName: "excel" */ '@/views/excel/Excel'),
        name: 'Excel',
        meta: { noCache: true, title: 'excel', icon: 'excel', no_permission: true }
      }
    ]
  },
  {
    path: '/error',
    component: Layout,
    redirect: '/error/404',
    hidden: true,
    meta: { title: '404', icon: 'index' },
    children: [
      {
        path: '404',
        component: () => import(/* webpackChunkName: "error_404" */ '@/views/error/404'),
        name: '404',
        meta: { title: '404', icon: 'index', no_permission: true }
      },
      {
        path: 'no_permission',
        component: () => import(/* webpackChunkName: "no_permission" */ '@/views/error/NoPermission'),
        name: 'NoPermission',
        meta: { title: 'no_permission', icon: 'index', no_permission: true }
      }
    ]
  },
  {
    path: '/account',
    component: Layout,
    redirect: '/account/setting',
    hidden: true,
    meta: { title: 'account', icon: 'index' },
    children: [
      {
        path: 'setting',
        component: () => import(/* webpackChunkName: "account-setting" */ '@/views/account/AccountSetting'),
        name: 'AccountSetting',
        meta: { noCache: true, title: 'accountSetting', icon: 'index', no_permission: true }
      }
    ]
  },
  {
    path: '/upgrade',
    component: Layout,
    redirect: '/upgrade/service',
    name: 'upgrade',
    hidden: true,
    meta: { title: '升级服务', icon: 'index' },
    children: [
      {
        path: 'service',
        component: () => import(/* webpackChunkName: "account-setting" */ '@/views/upgrade/upgradeService'),
        name: 'UpgradeService',
        meta: { noCache: true, title: 'upgradeService', icon: 'index', no_permission: true }
      }
    ]
  },
  {
    path: '/print',
    component: () => import(/* webpackChunkName: "login" */ '@/views/print'),
    hidden: true,
    name: 'Print',
    meta: {
      title: 'Print',
      noCache: true
    }
  },
  {
    path: '/agreement/:type?',
    component: () => import(/* webpackChunkName: "agreement" */ '@/views/agreement'),
    hidden: true,
    name: 'Agreement',
    meta: {
      title: 'Agreement',
      noCache: true
    }
  },
  {
    path: '/preview',
    component: () => import(/* webpackChunkName: "preview" */ '@/views/preview'),
    hidden: true,
    name: 'Preview',
    meta: {
      title: 'Preview',
      noCache: true
    }
  },
  {
    path: '/questionnaire_detail',
    component: () =>
      import(
        /* webpackChunkName: "notice_detail" */ '@/views/QuestionnaireDetail'
      ),
    name: 'QuestionnaireDetail',
    hidden: true,
    meta: {
      noCache: true,
      title: 'questionnaire_detail'
    }
  },
  // 设备管理，如果后面娜到运维平台就可以删了
  {
    path: '/opsDeviceAdmin',
    component: () => import(/* webpackChunkName: "login" */ '@/views/opsDeviceAdmin'),
    hidden: true,
    name: 'opsDeviceAdmin',
    meta: {
      title: 'opsDeviceAdmin',
      noCache: true
    }
  }
  // {
  //   path: '*',
  //   redirect: '/404',
  //   hidden: true,
  //   meta: { title: '404', icon: 'index' }
  // }
]
export const asyncRoutes = [
  ...otherRouter
]

const createRouter = () => new VueRouter({
  mode: 'hash', // 'history'
  base: process.env.BASE_URL,
  scrollBehavior: () => ({ y: 0 }),
  routes: defaultRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

// 解决导航栏或者底部导航tabBar中的vue-router在3.0版本以上频繁点击菜单报错的问题。
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}

const originalReplace = VueRouter.prototype.replace
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch(err => err)
}

export default router
